'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Calculator, History, RotateCcw, Delete } from 'lucide-react';

interface HistoryItem {
  equation: string;
  result: string;
  timestamp: Date;
}

export function SimpleCalculatorTool() {
  const [display, setDisplay] = useState('0');
  const [equation, setEquation] = useState('');
  const [isResult, setIsResult] = useState(false);
  const [waitingForOperand, setWaitingForOperand] = useState(true);
  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [showHistory, setShowHistory] = useState(false);
  
  const handleNumberClick = (num: string) => {
    if (isResult) {
      // إذا كانت النتيجة معروضة، ابدأ حساب جديد
      setDisplay(num);
      setEquation('');
      setIsResult(false);
      setWaitingForOperand(false);
    } else if (waitingForOperand) {
      // إذا كنا ننتظر رقم جديد، استبدل الشاشة
      setDisplay(num);
      setWaitingForOperand(false);
    } else {
      // إضافة الرقم إلى الرقم الحالي
      setDisplay(display === '0' ? num : display + num);
    }
  };

  const handleOperatorClick = (op: string) => {
    if (isResult) {
      // استخدام النتيجة السابقة للعملية الجديدة
      setEquation(display + ' ' + op);
      setWaitingForOperand(true);
      setIsResult(false);
    } else if (waitingForOperand) {
      // تغيير العملية الحالية إذا لم يتم إدخال رقم جديد
      if (equation.length > 0) {
        const parts = equation.trim().split(' ');
        if (parts.length >= 2) {
          // استبدال العملية الأخيرة
          parts[parts.length - 1] = op;
          setEquation(parts.join(' '));
        }
      }
    } else {
      // إضافة الرقم والعملية إلى المعادلة
      const newEquation = equation ? `${equation} ${display} ${op}` : `${display} ${op}`;
      setEquation(newEquation);
      setWaitingForOperand(true);
    }
  };

  const handleDecimalClick = () => {
    if (isResult) {
      // إذا كانت النتيجة معروضة، ابدأ رقم جديد بنقطة عشرية
      setDisplay('0.');
      setEquation('');
      setIsResult(false);
      setWaitingForOperand(false);
    } else if (waitingForOperand) {
      // إذا كنا ننتظر رقم جديد، ابدأ بـ 0.
      setDisplay('0.');
      setWaitingForOperand(false);
    } else if (!display.includes('.')) {
      // إضافة النقطة العشرية إذا لم تكن موجودة
      setDisplay(display + '.');
    }
  };

  const handleClearClick = () => {
    setDisplay('0');
    setEquation('');
    setIsResult(false);
    setWaitingForOperand(true);
  };

  const handleBackspaceClick = () => {
    if (waitingForOperand || isResult) return;
    
    if (display.length > 1) {
      setDisplay(display.substring(0, display.length - 1));
    } else {
      setDisplay('0');
      setWaitingForOperand(true);
    }
  };

  const addToHistory = (equation: string, result: string) => {
    const newItem: HistoryItem = {
      equation,
      result,
      timestamp: new Date()
    };
    setHistory(prev => [newItem, ...prev.slice(0, 9)]); // Keep only last 10 items
  };

  const clearHistory = () => {
    setHistory([]);
  };

  const handleEqualsClick = () => {
    try {
      // إذا لم تكن هناك معادلة أو كنا ننتظر رقم، لا تفعل شيئاً
      if (equation === '') {
        return;
      }

      // إضافة الرقم الأخير إلى المعادلة إذا لم نكن ننتظر رقم
      let fullEquation;
      if (waitingForOperand) {
        // إذا كنا ننتظر رقم، استخدم المعادلة كما هي (قد تكون ناقصة)
        fullEquation = equation.trim();
      } else {
        // إضافة الرقم الحالي إلى المعادلة
        fullEquation = `${equation} ${display}`;
      }

      // التحقق من أن المعادلة مكتملة
      const parts = fullEquation.trim().split(' ');
      if (parts.length < 3) {
        return; // المعادلة غير مكتملة
      }

      // استبدال علامات القسمة والضرب بعلامات جافاسكريبت
      const parsedEquation = fullEquation
        .replace(/×/g, '*')
        .replace(/÷/g, '/')
        .replace(/%/g, '/100*');

      // تحذير: استخدام eval يمكن أن يكون خطيراً لكن في هذه الحالة البسيطة مع القيود المفروضة، يمكن استخدامه
      const result = eval(parsedEquation);

      // التحقق من النتيجة وعرضها بشكل مناسب
      let finalResult;
      if (Number.isInteger(result)) {
        finalResult = result.toString();
      } else {
        finalResult = parseFloat(result.toFixed(8)).toString();
      }

      // إضافة العملية إلى التاريخ
      addToHistory(fullEquation, finalResult);

      setDisplay(finalResult);
      setEquation('');
      setIsResult(true);
      setWaitingForOperand(true);
    } catch (error) {
      setDisplay('خطأ');
      setEquation('');
      setIsResult(true);
      setWaitingForOperand(true);
    }
  };

  // Handle keyboard input
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      const { key } = event;

      if (key >= '0' && key <= '9') {
        handleNumberClick(key);
      } else if (key === '.') {
        handleDecimalClick();
      } else if (key === '+') {
        handleOperatorClick('+');
      } else if (key === '-') {
        handleOperatorClick('-');
      } else if (key === '*') {
        handleOperatorClick('×');
      } else if (key === '/') {
        event.preventDefault();
        handleOperatorClick('÷');
      } else if (key === 'Enter' || key === '=') {
        event.preventDefault();
        handleEqualsClick();
      } else if (key === 'Escape' || key === 'c' || key === 'C') {
        handleClearClick();
      } else if (key === 'Backspace') {
        handleBackspaceClick();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [display, equation, isResult, waitingForOperand]);

  const calculatorButtons = [
    { value: 'C', onClick: handleClearClick, variant: 'destructive', className: 'text-white font-bold' },
    { value: '±', onClick: () => {}, variant: 'secondary', className: 'text-muted-foreground' },
    { value: '%', onClick: () => handleOperatorClick('%'), variant: 'secondary', className: 'text-muted-foreground' },
    { value: '÷', onClick: () => handleOperatorClick('÷'), variant: 'secondary', className: 'bg-orange-500 hover:bg-orange-600 text-white font-bold' },

    { value: '7', onClick: () => handleNumberClick('7'), className: 'bg-gray-700 hover:bg-gray-600 text-white font-semibold' },
    { value: '8', onClick: () => handleNumberClick('8'), className: 'bg-gray-700 hover:bg-gray-600 text-white font-semibold' },
    { value: '9', onClick: () => handleNumberClick('9'), className: 'bg-gray-700 hover:bg-gray-600 text-white font-semibold' },
    { value: '×', onClick: () => handleOperatorClick('×'), variant: 'secondary', className: 'bg-orange-500 hover:bg-orange-600 text-white font-bold' },

    { value: '4', onClick: () => handleNumberClick('4'), className: 'bg-gray-700 hover:bg-gray-600 text-white font-semibold' },
    { value: '5', onClick: () => handleNumberClick('5'), className: 'bg-gray-700 hover:bg-gray-600 text-white font-semibold' },
    { value: '6', onClick: () => handleNumberClick('6'), className: 'bg-gray-700 hover:bg-gray-600 text-white font-semibold' },
    { value: '-', onClick: () => handleOperatorClick('-'), variant: 'secondary', className: 'bg-orange-500 hover:bg-orange-600 text-white font-bold' },

    { value: '1', onClick: () => handleNumberClick('1'), className: 'bg-gray-700 hover:bg-gray-600 text-white font-semibold' },
    { value: '2', onClick: () => handleNumberClick('2'), className: 'bg-gray-700 hover:bg-gray-600 text-white font-semibold' },
    { value: '3', onClick: () => handleNumberClick('3'), className: 'bg-gray-700 hover:bg-gray-600 text-white font-semibold' },
    { value: '+', onClick: () => handleOperatorClick('+'), variant: 'secondary', className: 'bg-orange-500 hover:bg-orange-600 text-white font-bold' },

    { value: '0', onClick: () => handleNumberClick('0'), className: 'bg-gray-700 hover:bg-gray-600 text-white font-semibold col-span-2' },
    { value: '.', onClick: handleDecimalClick, className: 'bg-gray-700 hover:bg-gray-600 text-white font-semibold' },
    { value: '=', onClick: handleEqualsClick, variant: 'default', className: 'bg-orange-500 hover:bg-orange-600 text-white font-bold' },
  ];

  return (
    <div className="w-full max-w-md mx-auto">
      <Card className="bg-gradient-to-br from-gray-900 to-gray-800 border-gray-700 shadow-2xl">
        <CardHeader className="text-center pb-4">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Calculator className="w-6 h-6 text-orange-500" />
            <CardTitle className="text-white text-xl">آلة حاسبة متقدمة</CardTitle>
          </div>
          <CardDescription className="text-gray-300">
            إجراء العمليات الحسابية مع دعم لوحة المفاتيح والتاريخ
          </CardDescription>
          <div className="flex justify-center gap-2 mt-3">
            <Badge variant="secondary" className="text-xs">
              دعم لوحة المفاتيح
            </Badge>
            <Badge variant="secondary" className="text-xs">
              تاريخ العمليات
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Display Area */}
          <div className="bg-black rounded-lg p-4 border border-gray-600">
            <div className="text-right">
              {equation && (
                <div className="text-sm text-gray-400 font-mono mb-1 overflow-x-auto">
                  {equation} {isResult ? '' : display}
                </div>
              )}
              <div className="text-3xl font-mono text-white font-bold overflow-x-auto">
                {display}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowHistory(!showHistory)}
              className="flex items-center gap-2 text-gray-300 border-gray-600 hover:bg-gray-700"
            >
              <History className="w-4 h-4" />
              التاريخ
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleBackspaceClick}
              className="flex items-center gap-2 text-gray-300 border-gray-600 hover:bg-gray-700"
            >
              <Delete className="w-4 h-4" />
              مسح
            </Button>
          </div>

          {/* History Panel */}
          {showHistory && (
            <div className="bg-gray-800 rounded-lg p-3 border border-gray-600 max-h-40 overflow-y-auto">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-sm font-semibold text-white">تاريخ العمليات</h3>
                {history.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearHistory}
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    <RotateCcw className="w-3 h-3 mr-1" />
                    مسح الكل
                  </Button>
                )}
              </div>
              {history.length === 0 ? (
                <p className="text-xs text-gray-500 text-center py-2">لا توجد عمليات سابقة</p>
              ) : (
                <div className="space-y-1">
                  {history.map((item, index) => (
                    <div
                      key={index}
                      className="text-xs font-mono text-gray-300 p-2 bg-gray-700 rounded cursor-pointer hover:bg-gray-600 transition-colors"
                      onClick={() => {
                        setDisplay(item.result);
                        setEquation('');
                        setIsResult(true);
                        setWaitingForOperand(true);
                      }}
                    >
                      <div className="flex justify-between">
                        <span>{item.equation} = {item.result}</span>
                        <span className="text-gray-500">
                          {item.timestamp.toLocaleTimeString('ar-SA', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          <Separator className="bg-gray-600" />

          {/* Calculator Buttons */}
          <div className="grid grid-cols-4 gap-2">
            {calculatorButtons.map((button, index) => (
              <Button
                key={index}
                variant={button.variant as any || 'outline'}
                onClick={button.onClick}
                className={`h-14 text-lg font-semibold transition-all duration-200 hover:scale-105 ${button.className || ''} ${
                  button.value === '0' ? 'col-span-2' : ''
                }`}
              >
                {button.value}
              </Button>
            ))}
          </div>

          {/* Keyboard Shortcuts Info */}
          <div className="text-xs text-gray-500 text-center mt-4">
            <p>اختصارات لوحة المفاتيح: الأرقام، +، -، *، /، Enter (=)، Escape (C)، Backspace</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
